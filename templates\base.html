<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Twitter Clone{% endblock %}</title>
    
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Dark mode configuration -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'twitter-blue': '#1DA1F2',
                        'twitter-dark-blue': '#1A91DA',
                        'dark-bg': '#15202B',
                        'dark-secondary': '#192734',
                        'dark-border': '#38444D',
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        .tweet-hover:hover {
            background-color: rgba(29, 161, 242, 0.1);
        }
        .dark .tweet-hover:hover {
            background-color: rgba(29, 161, 242, 0.1);
        }
    </style>
</head>
<body class="h-full bg-white dark:bg-dark-bg text-gray-900 dark:text-white transition-colors duration-200">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-dark-bg border-b border-gray-200 dark:border-dark-border sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{% url 'posts:home' %}" class="flex-shrink-0 flex items-center">
                        <i class="fab fa-twitter text-twitter-blue text-2xl"></i>
                        <span class="ml-2 text-xl font-bold">Twitter Clone</span>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <div class="flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <!-- Create Post Button -->
                        <button onclick="togglePostModal()" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-4 py-2 rounded-full font-semibold transition-colors">
                            Tweet
                        </button>
                        
                        <!-- User Menu -->
                        <div class="relative">
                            <button onclick="toggleUserMenu()" class="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                                {% if user.avatar_url %}
                                    <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-8 h-8 rounded-full">
                                {% else %}
                                    <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-sm"></i>
                                    </div>
                                {% endif %}
                                <span class="hidden md:block">{{ user.full_name }}</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            
                            <!-- Dropdown Menu -->
                            <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-dark-secondary rounded-md shadow-lg py-1 z-50">
                                <a href="{% url 'accounts:profile' user.username %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="{% url 'accounts:edit_profile' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </a>
                                <div class="border-t border-gray-100 dark:border-gray-600"></div>
                                <a href="{% url 'accounts:logout' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                            Login
                        </a>
                        <a href="{% url 'accounts:register' %}" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-4 py-2 rounded-full font-semibold transition-colors">
                            Sign up
                        </a>
                    {% endif %}
                    
                    <!-- Dark Mode Toggle -->
                    <button onclick="toggleDarkMode()" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i id="darkModeIcon" class="fas fa-moon text-gray-600 dark:text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        <!-- Messages -->
        {% if messages %}
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                {% for message in messages %}
                    <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700 dark:bg-red-900 dark:border-red-600 dark:text-red-200{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700 dark:bg-green-900 dark:border-green-600 dark:text-green-200{% else %}bg-blue-100 border border-blue-400 text-blue-700 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        {% block content %}
        {% endblock %}
    </main>

    <!-- Create Post Modal -->
    {% if user.is_authenticated %}
    <div id="postModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-dark-secondary rounded-lg max-w-lg w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Create Tweet</h3>
                <button onclick="togglePostModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="post" action="{% url 'posts:create_post' %}">
                {% csrf_token %}
                <div class="mb-4">
                    <textarea name="content" placeholder="What's happening?" maxlength="280" 
                              class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-twitter-blue dark:bg-dark-bg dark:text-white" 
                              rows="4" required></textarea>
                    <div class="text-right text-sm text-gray-500 mt-1">
                        <span id="charCount">0</span>/280
                    </div>
                </div>
                
                <div class="mb-4">
                    <input type="url" name="image_url" placeholder="Image URL (optional)" 
                           class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-twitter-blue dark:bg-dark-bg dark:text-white">
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors">
                        Tweet
                    </button>
                </div>
            </form>
        </div>
    </div>
    {% endif %}

    <!-- JavaScript -->
    <script>
        // Dark mode functionality
        function toggleDarkMode() {
            const html = document.documentElement;
            const icon = document.getElementById('darkModeIcon');
            
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                icon.className = 'fas fa-moon text-gray-600';
                localStorage.setItem('darkMode', 'false');
            } else {
                html.classList.add('dark');
                icon.className = 'fas fa-sun text-yellow-400';
                localStorage.setItem('darkMode', 'true');
            }
        }

        // Initialize dark mode
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
            document.getElementById('darkModeIcon').className = 'fas fa-sun text-yellow-400';
        }

        // User menu toggle
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            menu.classList.toggle('hidden');
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('userMenu');
            const button = event.target.closest('button[onclick="toggleUserMenu()"]');
            
            if (!button && !menu.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });

        // Post modal functionality
        function togglePostModal() {
            const modal = document.getElementById('postModal');
            modal.classList.toggle('hidden');
        }

        // Character counter for tweet
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.querySelector('textarea[name="content"]');
            const charCount = document.getElementById('charCount');
            
            if (textarea && charCount) {
                textarea.addEventListener('input', function() {
                    charCount.textContent = this.value.length;
                    
                    if (this.value.length > 280) {
                        charCount.classList.add('text-red-500');
                    } else {
                        charCount.classList.remove('text-red-500');
                    }
                });
            }
        });

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('postModal');
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        });
    </script>

    {% block extra_js %}
    {% endblock %}
</body>
</html>
