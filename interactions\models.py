from django.db import models
from django.conf import settings
import uuid

class Like(models.Model):
    """
    Like model for post likes
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='likes')
    post = models.ForeignKey('posts.Post', on_delete=models.CASCADE, related_name='likes')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'post')
        db_table = 'likes'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} likes {self.post.id}"

class Comment(models.Model):
    """
    Comment model for post comments
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='comments')
    post = models.ForeignKey('posts.Post', on_delete=models.CASCADE, related_name='comments')
    content = models.TextField(max_length=280)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'comments'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}: {self.content[:50]}..."

    def get_time_since_posted(self):
        """Get human readable time since comment was created"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - self.created_at

        if diff < timedelta(minutes=1):
            return "now"
        elif diff < timedelta(hours=1):
            return f"{diff.seconds // 60}m"
        elif diff < timedelta(days=1):
            return f"{diff.seconds // 3600}h"
        elif diff < timedelta(days=7):
            return f"{diff.days}d"
        else:
            return self.created_at.strftime("%b %d")

    @property
    def content_preview(self):
        """Get preview of content for display"""
        if len(self.content) > 100:
            return self.content[:100] + "..."
        return self.content
