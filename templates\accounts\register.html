{% extends 'base.html' %}

{% block title %}Sign up - Twitter Clone{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-bg py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-twitter-blue">
                <i class="fab fa-twitter text-white text-2xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Or
                <a href="{% url 'accounts:login' %}" class="font-medium text-twitter-blue hover:text-twitter-dark-blue">
                    sign in to your existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="space-y-4">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.username.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.display_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Display Name</label>
                    {{ form.display_name }}
                    {% if form.display_name.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.display_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.password1.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Confirm Password</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.password2.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="text-sm text-red-600 dark:text-red-400">
                    {{ form.non_field_errors.0 }}
                </div>
            {% endif %}

            <div class="flex items-center">
                <input id="agree-terms" name="agree-terms" type="checkbox" class="h-4 w-4 text-twitter-blue focus:ring-twitter-blue border-gray-300 rounded" required>
                <label for="agree-terms" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                    I agree to the <a href="#" class="text-twitter-blue hover:text-twitter-dark-blue">Terms of Service</a> and <a href="#" class="text-twitter-blue hover:text-twitter-dark-blue">Privacy Policy</a>
                </label>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-twitter-blue hover:bg-twitter-dark-blue focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-twitter-blue transition-colors">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-user-plus text-twitter-dark-blue group-hover:text-twitter-blue"></i>
                    </span>
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
