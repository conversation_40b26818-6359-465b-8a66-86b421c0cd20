# Twitter Clone - منصة تشبه تويتر

منصة تواصل اجتماعي تشبه تويتر مبنية باستخدام Django و Supabase مع تصميم عصري باستخدام TailwindCSS ودعم الوضع الليلي.

## المزايا الرئيسية

- ✅ نظام تسجيل دخول وتسجيل مستخدمين جديد
- ✅ صفحة رئيسية (Feed) لعرض التغريدات بالترتيب الزمني
- ✅ إمكانية نشر تغريدات (نص + صورة)
- ✅ نظام إعجابات وتعليقات على التغريدات
- ✅ نظام متابعة/إلغاء متابعة بين المستخدمين
- ✅ صفحة شخصية (Profile) تعرض بيانات المستخدم وتغريداته
- ✅ تصميم يشبه تويتر مع دعم الوضع الليلي (Dark Mode)
- ✅ تصميم متجاوب يعمل على جميع الأجهزة

## التقنيات المستخدمة

- **Backend**: Django 4.2.7 + Django REST Framework
- **Database**: Supabase (PostgreSQL)
- **Frontend**: HTML5 + TailwindCSS + JavaScript
- **Authentication**: Django Auth + Supabase Auth
- **Icons**: Font Awesome 6

## متطلبات النظام

- Python 3.8+
- pip (Python package manager)
- حساب Supabase (مجاني)

## خطوات التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd twitter-clone
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات في Supabase

1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك: `qxbcbqazuhpckmdqsfyr`
3. اذهب إلى SQL Editor
4. قم بتشغيل سكريبت إعداد قاعدة البيانات:

```bash
python setup_supabase_tables.py
```

5. انسخ والصق كل أمر SQL في محرر SQL واحداً تلو الآخر

### 4. إعداد متغيرات البيئة

تأكد من أن ملف `.env` يحتوي على:
```
SUPABASE_URL=https://qxbcbqazuhpckmdqsfyr.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4YmNicWF6dWhwY2ttZHFzZnlyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3MjMwNzIsImV4cCI6MjA3MjI5OTA3Mn0.egGLHuY3R3SK7EZO243ob7XEi4WE-3PnfEhXz6vto_0
SECRET_KEY=django-insecure-your-secret-key-here-change-in-production
DEBUG=True
```

### 5. إجراء الهجرات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. إنشاء مستخدم إداري (اختياري)
```bash
python manage.py createsuperuser
```

### 7. تشغيل الخادم
```bash
python manage.py runserver
```

### 8. فتح المتصفح
اذهب إلى: `http://127.0.0.1:8000`

## هيكل المشروع

```
twitter_clone/
├── accounts/           # تطبيق المستخدمين والمصادقة
├── posts/             # تطبيق المنشورات
├── follows/           # تطبيق المتابعات
├── interactions/      # تطبيق الإعجابات والتعليقات
├── templates/         # قوالب HTML
├── static/           # ملفات CSS/JS/Images
├── media/            # ملفات المستخدمين المرفوعة
├── twitter_clone/    # إعدادات المشروع الرئيسية
├── requirements.txt  # متطلبات Python
├── setup_supabase_tables.py  # سكريبت إعداد قاعدة البيانات
└── README.md         # هذا الملف
```

## كيفية الاستخدام

### 1. إنشاء حساب جديد
- اذهب إلى `/accounts/register/`
- املأ البيانات المطلوبة
- سيتم تسجيل دخولك تلقائياً

### 2. تسجيل الدخول
- اذهب إلى `/accounts/login/`
- أدخل اسم المستخدم وكلمة المرور

### 3. نشر تغريدة
- من الصفحة الرئيسية، اكتب في مربع "What's happening?"
- يمكنك إضافة رابط صورة (اختياري)
- اضغط "Tweet"

### 4. التفاعل مع التغريدات
- **الإعجاب**: اضغط على أيقونة القلب
- **التعليق**: اضغط على أيقونة التعليق أو اذهب لصفحة التغريدة
- **المشاركة**: اضغط على أيقونة المشاركة

### 5. متابعة المستخدمين
- اذهب إلى ملف المستخدم الشخصي
- اضغط "Follow" للمتابعة أو "Following" لإلغاء المتابعة

### 6. تعديل الملف الشخصي
- اذهب إلى ملفك الشخصي
- اضغط "Edit profile"
- عدّل البيانات واضغط "Save"

## المزايا المتقدمة

### الوضع الليلي (Dark Mode)
- اضغط على أيقونة القمر/الشمس في شريط التنقل
- سيتم حفظ تفضيلك في المتصفح

### التصميم المتجاوب
- يعمل المشروع بشكل مثالي على:
  - أجهزة الكمبيوتر المكتبية
  - الأجهزة اللوحية
  - الهواتف الذكية

### الأمان
- Row Level Security (RLS) مفعل في Supabase
- حماية CSRF في Django
- تشفير كلمات المرور
- التحقق من صحة البيانات

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة `SUPABASE_URL` و `SUPABASE_KEY`
- تأكد من تشغيل جميع أوامر SQL في Supabase

### خطأ في تحميل الصور
- تأكد من أن روابط الصور صحيحة وقابلة للوصول
- تأكد من أن الروابط تنتهي بامتداد صورة (.jpg, .png, إلخ)

### مشاكل في التصميم
- تأكد من اتصالك بالإنترنت (TailwindCSS يُحمل من CDN)
- امسح cache المتصفح

## التطوير المستقبلي

- [ ] رفع الصور مباشرة إلى Supabase Storage
- [ ] نظام الإشعارات
- [ ] البحث عن المستخدمين والتغريدات
- [ ] الرسائل الخاصة
- [ ] إعادة التغريد (Retweet)
- [ ] الهاشتاغات
- [ ] التحقق من الحسابات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## الدعم

إذا واجهت أي مشاكل، يرجى:
1. التحقق من قسم استكشاف الأخطاء أعلاه
2. مراجعة logs الخادم
3. التأكد من إعداد Supabase بشكل صحيح

---

**تم تطوير هذا المشروع بواسطة Augment Agent** 🚀
