# تعليمات التشغيل السريع - Twitter Clone

## خطوات التشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعد<PERSON> قاعدة البيانات في Supabase
```bash
python setup_supabase_tables.py
```
انسخ والصق أوامر SQL في Supabase SQL Editor

### 3. إجراء الهجرات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. تشغيل الخادم
```bash
python manage.py runserver
```

### 5. فتح المتصفح
اذهب إلى: `http://127.0.0.1:8000`

## الروابط المهمة

- **الصفحة الرئيسية**: `http://127.0.0.1:8000/`
- **تسجيل حساب جديد**: `http://127.0.0.1:8000/accounts/register/`
- **تسجيل الدخول**: `http://127.0.0.1:8000/accounts/login/`
- **لوحة الإدارة**: `http://127.0.0.1:8000/admin/`

## ملاحظات مهمة

1. **قاعدة البيانات**: تأكد من تشغيل جميع أوامر SQL في Supabase قبل استخدام الموقع
2. **الصور**: استخدم روابط صور صحيحة (jpg, png, gif, webp)
3. **الوضع الليلي**: اضغط على أيقونة القمر/الشمس في شريط التنقل
4. **المتطلبات**: Python 3.8+ مطلوب

## اختبار المزايا

✅ إنشاء حساب جديد
✅ تسجيل الدخول
✅ نشر تغريدة
✅ إضافة صورة للتغريدة
✅ الإعجاب بالتغريدات
✅ التعليق على التغريدات
✅ متابعة المستخدمين
✅ عرض الملف الشخصي
✅ تعديل الملف الشخصي
✅ الوضع الليلي

## في حالة وجود مشاكل

1. تأكد من تثبيت جميع المتطلبات
2. تأكد من إعداد Supabase بشكل صحيح
3. تأكد من صحة متغيرات البيئة في `.env`
4. راجع ملف `README.md` للتفاصيل الكاملة
