from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Post
from interactions.models import Like, Comment
from follows.models import Follow

def home_view(request):
    """Home page with feed of posts"""
    if request.user.is_authenticated:
        # Get posts from users that the current user follows + own posts
        following_users = Follow.objects.filter(follower=request.user).values_list('following', flat=True)
        posts = Post.objects.filter(
            Q(user__in=following_users) | Q(user=request.user)
        ).select_related('user').prefetch_related('likes', 'comments')
    else:
        # Show all posts for anonymous users
        posts = Post.objects.all().select_related('user').prefetch_related('likes', 'comments')

    # Add is_liked information for each post
    if request.user.is_authenticated:
        for post in posts:
            post.is_liked = post.likes.filter(user=request.user).exists()
    else:
        for post in posts:
            post.is_liked = False

    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Add is_liked information for paginated posts too
    if request.user.is_authenticated:
        for post in page_obj:
            post.is_liked = post.likes.filter(user=request.user).exists()
    else:
        for post in page_obj:
            post.is_liked = False

    context = {
        'posts': page_obj,
        'page_obj': page_obj,
    }

    return render(request, 'posts/home.html', context)

@login_required
def create_post_view(request):
    """Create a new post"""
    if request.method == 'POST':
        content = request.POST.get('content', '').strip()
        image_url = request.POST.get('image_url', '').strip()

        if not content:
            messages.error(request, 'Post content cannot be empty.')
            return redirect('posts:home')

        if len(content) > 280:
            messages.error(request, 'Post content cannot exceed 280 characters.')
            return redirect('posts:home')

        post = Post.objects.create(
            user=request.user,
            content=content,
            image_url=image_url if image_url else None
        )

        messages.success(request, 'Post created successfully!')
        return redirect('posts:home')

    return redirect('posts:home')

def post_detail_view(request, post_id):
    """View individual post with comments"""
    post = get_object_or_404(Post, id=post_id)
    comments = Comment.objects.filter(post=post).select_related('user')

    # Add is_liked information for the post
    if request.user.is_authenticated:
        post.is_liked = post.likes.filter(user=request.user).exists()
    else:
        post.is_liked = False

    context = {
        'post': post,
        'comments': comments,
    }

    return render(request, 'posts/post_detail.html', context)

@login_required
@require_http_methods(["POST"])
def like_post(request, post_id):
    """Like/unlike a post via AJAX"""
    post = get_object_or_404(Post, id=post_id)

    like_obj, created = Like.objects.get_or_create(
        user=request.user,
        post=post
    )

    if not created:
        # Already liked, so unlike
        like_obj.delete()
        is_liked = False
        action = 'unliked'
    else:
        is_liked = True
        action = 'liked'

    return JsonResponse({
        'is_liked': is_liked,
        'action': action,
        'likes_count': post.get_likes_count()
    })

@login_required
@require_http_methods(["POST"])
def add_comment(request, post_id):
    """Add a comment to a post"""
    post = get_object_or_404(Post, id=post_id)
    content = request.POST.get('content', '').strip()

    if not content:
        return JsonResponse({'error': 'Comment content cannot be empty'}, status=400)

    if len(content) > 280:
        return JsonResponse({'error': 'Comment cannot exceed 280 characters'}, status=400)

    comment = Comment.objects.create(
        user=request.user,
        post=post,
        content=content
    )

    return JsonResponse({
        'success': True,
        'comment': {
            'id': str(comment.id),
            'content': comment.content,
            'user': {
                'username': comment.user.username,
                'display_name': comment.user.full_name,
                'avatar_url': comment.user.avatar_url
            },
            'created_at': comment.get_time_since_posted()
        },
        'comments_count': post.get_comments_count()
    })

@login_required
@require_http_methods(["POST"])
def delete_post(request, post_id):
    """Delete a post (only by owner)"""
    post = get_object_or_404(Post, id=post_id, user=request.user)
    post.delete()
    messages.success(request, 'Post deleted successfully!')
    return redirect('posts:home')
