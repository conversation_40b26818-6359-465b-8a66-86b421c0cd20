{% extends 'base.html' %}

{% block title %}{{ profile_user.full_name }} (@{{ profile_user.username }}) - Twitter Clone{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto bg-white dark:bg-dark-secondary">
    <!-- Profile Header -->
    <div class="relative">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500 relative">
            {% if profile_user.cover_url %}
                <img src="{{ profile_user.cover_url }}" alt="Cover" class="w-full h-full object-cover">
            {% endif %}
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>
        
        <!-- Profile Info -->
        <div class="px-4 pb-4">
            <!-- Avatar and Edit Button -->
            <div class="flex justify-between items-end -mt-16 mb-4">
                <div class="relative">
                    {% if profile_user.avatar_url %}
                        <img src="{{ profile_user.avatar_url }}" alt="{{ profile_user.username }}" 
                             class="w-32 h-32 rounded-full border-4 border-white dark:border-dark-secondary bg-white dark:bg-dark-secondary">
                    {% else %}
                        <div class="w-32 h-32 rounded-full border-4 border-white dark:border-dark-secondary bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <i class="fas fa-user text-4xl text-gray-600 dark:text-gray-400"></i>
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    {% if is_own_profile %}
                        <a href="{% url 'accounts:edit_profile' %}" 
                           class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-full font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            Edit profile
                        </a>
                    {% else %}
                        {% if user.is_authenticated %}
                            <button onclick="followUser('{{ profile_user.username }}')" 
                                    id="followButton"
                                    class="px-4 py-2 rounded-full font-semibold transition-colors {% if is_following %}bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600{% else %}bg-twitter-blue hover:bg-twitter-dark-blue text-white{% endif %}">
                                <span id="followButtonText">{% if is_following %}Following{% else %}Follow{% endif %}</span>
                            </button>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
            
            <!-- User Details -->
            <div class="space-y-3">
                <div>
                    <h1 class="text-xl font-bold text-gray-900 dark:text-white">{{ profile_user.full_name }}</h1>
                    <p class="text-gray-500 dark:text-gray-400">@{{ profile_user.username }}</p>
                </div>
                
                {% if profile_user.bio %}
                <p class="text-gray-900 dark:text-white">{{ profile_user.bio }}</p>
                {% endif %}
                
                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    {% if profile_user.location %}
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        {{ profile_user.location }}
                    </div>
                    {% endif %}
                    
                    {% if profile_user.website %}
                    <div class="flex items-center">
                        <i class="fas fa-link mr-1"></i>
                        <a href="{{ profile_user.website }}" target="_blank" class="text-twitter-blue hover:underline">
                            {{ profile_user.website|slice:":30" }}{% if profile_user.website|length > 30 %}...{% endif %}
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="flex items-center">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        Joined {{ profile_user.created_at|date:"F Y" }}
                    </div>
                </div>
                
                <!-- Following/Followers Stats -->
                <div class="flex space-x-6 text-sm">
                    <div>
                        <span class="font-bold text-gray-900 dark:text-white" id="followingCount">{{ profile_user.get_following_count }}</span>
                        <span class="text-gray-500 dark:text-gray-400">Following</span>
                    </div>
                    <div>
                        <span class="font-bold text-gray-900 dark:text-white" id="followersCount">{{ profile_user.get_followers_count }}</span>
                        <span class="text-gray-500 dark:text-gray-400">Followers</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="border-b border-gray-200 dark:border-dark-border">
        <nav class="flex">
            <button class="flex-1 py-4 px-1 text-center border-b-2 border-twitter-blue text-twitter-blue font-medium">
                Tweets
                <span class="ml-1 text-sm text-gray-500 dark:text-gray-400">({{ profile_user.get_posts_count }})</span>
            </button>
            <button class="flex-1 py-4 px-1 text-center border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                Tweets & replies
            </button>
            <button class="flex-1 py-4 px-1 text-center border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                Media
            </button>
            <button class="flex-1 py-4 px-1 text-center border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                Likes
            </button>
        </nav>
    </div>
    
    <!-- Posts -->
    <div>
        {% for post in posts %}
        <article class="border-b border-gray-200 dark:border-dark-border p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div class="flex space-x-3">
                <!-- User Avatar -->
                <div class="flex-shrink-0">
                    {% if post.user.avatar_url %}
                        <img src="{{ post.user.avatar_url }}" alt="{{ post.user.username }}" class="w-12 h-12 rounded-full">
                    {% else %}
                        <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Post Content -->
                <div class="flex-1 min-w-0">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2 mb-1">
                        <span class="font-bold">{{ post.user.full_name }}</span>
                        <span class="text-gray-500 dark:text-gray-400">@{{ post.user.username }}</span>
                        <span class="text-gray-500 dark:text-gray-400">·</span>
                        <span class="text-gray-500 dark:text-gray-400">{{ post.get_time_since_posted }}</span>
                    </div>
                    
                    <!-- Post Text -->
                    <div class="mb-3">
                        <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ post.content }}</p>
                    </div>
                    
                    <!-- Post Image -->
                    {% if post.image_url %}
                    <div class="mb-3">
                        <img src="{{ post.image_url }}" alt="Post image" class="rounded-2xl max-w-full h-auto border border-gray-200 dark:border-gray-600">
                    </div>
                    {% endif %}
                    
                    <!-- Post Actions -->
                    <div class="flex items-center justify-between max-w-md text-gray-500 dark:text-gray-400">
                        <!-- Comments -->
                        <a href="{% url 'posts:post_detail' post.id %}" class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                                <i class="far fa-comment"></i>
                            </div>
                            <span class="text-sm">{{ post.get_comments_count }}</span>
                        </a>
                        
                        <!-- Retweet -->
                        <button class="flex items-center space-x-2 hover:text-green-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900">
                                <i class="fas fa-retweet"></i>
                            </div>
                            <span class="text-sm">0</span>
                        </button>
                        
                        <!-- Like -->
                        {% if user.is_authenticated %}
                        <button onclick="likePost('{{ post.id }}')" class="flex items-center space-x-2 hover:text-red-500 transition-colors group {% if post.is_liked %}text-red-500{% endif %}">
                            <div class="p-2 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900">
                                <i class="{% if post.is_liked %}fas{% else %}far{% endif %} fa-heart"></i>
                            </div>
                            <span class="text-sm" id="likes-count-{{ post.id }}">{{ post.get_likes_count }}</span>
                        </button>
                        {% else %}
                        <div class="flex items-center space-x-2 text-gray-400">
                            <div class="p-2 rounded-full">
                                <i class="far fa-heart"></i>
                            </div>
                            <span class="text-sm">{{ post.get_likes_count }}</span>
                        </div>
                        {% endif %}
                        
                        <!-- Share -->
                        <button class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                                <i class="fas fa-share"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </article>
        {% empty %}
        <div class="p-8 text-center text-gray-500 dark:text-gray-400">
            <i class="fas fa-twitter text-4xl mb-4"></i>
            <h3 class="text-xl font-semibold mb-2">No tweets yet</h3>
            {% if is_own_profile %}
                <p>Start tweeting to share your thoughts with the world!</p>
            {% else %}
                <p>{{ profile_user.full_name }} hasn't tweeted yet.</p>
            {% endif %}
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Follow/Unfollow functionality
    function followUser(username) {
        fetch(`/accounts/follow/${username}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            const followButton = document.getElementById('followButton');
            const followButtonText = document.getElementById('followButtonText');
            const followersCount = document.getElementById('followersCount');
            
            if (data.is_following) {
                followButton.className = 'px-4 py-2 rounded-full font-semibold transition-colors bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600';
                followButtonText.textContent = 'Following';
            } else {
                followButton.className = 'px-4 py-2 rounded-full font-semibold transition-colors bg-twitter-blue hover:bg-twitter-dark-blue text-white';
                followButtonText.textContent = 'Follow';
            }
            
            followersCount.textContent = data.followers_count;
        })
        .catch(error => console.error('Error:', error));
    }

    // Like post functionality
    function likePost(postId) {
        fetch(`/posts/${postId}/like/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            const likesCount = document.getElementById(`likes-count-${postId}`);
            const likeButton = likesCount.parentElement;
            const heartIcon = likeButton.querySelector('i');
            
            likesCount.textContent = data.likes_count;
            
            if (data.is_liked) {
                likeButton.classList.add('text-red-500');
                heartIcon.classList.remove('far');
                heartIcon.classList.add('fas');
            } else {
                likeButton.classList.remove('text-red-500');
                heartIcon.classList.remove('fas');
                heartIcon.classList.add('far');
            }
        })
        .catch(error => console.error('Error:', error));
    }
</script>
{% endblock %}
