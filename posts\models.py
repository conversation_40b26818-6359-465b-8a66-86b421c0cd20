from django.db import models
from django.conf import settings
import uuid

class Post(models.Model):
    """
    Post model for tweets/posts
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='posts')
    content = models.TextField(max_length=280)  # Twitter-like character limit
    image_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        db_table = 'posts'

    def __str__(self):
        return f"{self.user.username}: {self.content[:50]}..."

    def get_likes_count(self):
        """Get number of likes for this post"""
        return self.likes.count()

    def get_comments_count(self):
        """Get number of comments for this post"""
        return self.comments.count()

    def is_liked_by(self, user):
        """Check if post is liked by specific user"""
        if user.is_authenticated:
            return self.likes.filter(user=user).exists()
        return False

    def get_time_since_posted(self):
        """Get human readable time since post was created"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - self.created_at

        if diff < timedelta(minutes=1):
            return "now"
        elif diff < timedelta(hours=1):
            return f"{diff.seconds // 60}m"
        elif diff < timedelta(days=1):
            return f"{diff.seconds // 3600}h"
        elif diff < timedelta(days=7):
            return f"{diff.days}d"
        else:
            return self.created_at.strftime("%b %d")

    @property
    def content_preview(self):
        """Get preview of content for display"""
        if len(self.content) > 100:
            return self.content[:100] + "..."
        return self.content
