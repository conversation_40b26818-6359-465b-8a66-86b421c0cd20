{% extends 'base.html' %}

{% block title %}Edit Profile - Twitter Clone{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto bg-white dark:bg-dark-secondary">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-border">
        <div class="flex items-center">
            <a href="{% url 'accounts:profile' user.username %}" class="mr-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <i class="fas fa-times text-gray-600 dark:text-gray-400"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">Edit profile</h1>
        </div>
        <button type="submit" form="profileForm" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors">
            Save
        </button>
    </div>

    <!-- Form -->
    <form id="profileForm" method="post" class="p-4 space-y-6">
        {% csrf_token %}
        
        <!-- Cover Image Section -->
        <div class="relative">
            <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg relative overflow-hidden">
                {% if user.cover_url %}
                    <img src="{{ user.cover_url }}" alt="Cover" class="w-full h-full object-cover">
                {% endif %}
                <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <button type="button" class="p-3 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors">
                        <i class="fas fa-camera text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Avatar -->
            <div class="absolute -bottom-16 left-4">
                <div class="relative">
                    {% if user.avatar_url %}
                        <img src="{{ user.avatar_url }}" alt="{{ user.username }}" 
                             class="w-32 h-32 rounded-full border-4 border-white dark:border-dark-secondary bg-white dark:bg-dark-secondary">
                    {% else %}
                        <div class="w-32 h-32 rounded-full border-4 border-white dark:border-dark-secondary bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <i class="fas fa-user text-4xl text-gray-600 dark:text-gray-400"></i>
                        </div>
                    {% endif %}
                    <button type="button" class="absolute inset-0 bg-black bg-opacity-40 rounded-full flex items-center justify-center text-white hover:bg-opacity-60 transition-colors">
                        <i class="fas fa-camera text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Form Fields -->
        <div class="pt-16 space-y-6">
            <!-- Display Name -->
            <div>
                <label for="{{ form.display_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Display Name
                </label>
                {{ form.display_name }}
                {% if form.display_name.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.display_name.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Bio -->
            <div>
                <label for="{{ form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Bio
                </label>
                {{ form.bio }}
                <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Tell people a little about yourself
                </div>
                {% if form.bio.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.bio.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Location -->
            <div>
                <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Location
                </label>
                {{ form.location }}
                {% if form.location.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.location.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Website -->
            <div>
                <label for="{{ form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Website
                </label>
                {{ form.website }}
                {% if form.website.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.website.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Avatar URL -->
            <div>
                <label for="{{ form.avatar_url.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Avatar Image URL
                </label>
                {{ form.avatar_url }}
                <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Enter a URL to your profile picture
                </div>
                {% if form.avatar_url.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.avatar_url.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- Cover URL -->
            <div>
                <label for="{{ form.cover_url.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Cover Image URL
                </label>
                {{ form.cover_url }}
                <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Enter a URL to your cover photo
                </div>
                {% if form.cover_url.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ form.cover_url.errors.0 }}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-dark-border">
            <a href="{% url 'accounts:profile' user.username %}" 
               class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-full font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                Cancel
            </a>
            <button type="submit" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors">
                Save changes
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Preview images when URLs are entered
    document.getElementById('{{ form.avatar_url.id_for_label }}').addEventListener('input', function() {
        const url = this.value.trim();
        const avatarImg = document.querySelector('.w-32.h-32 img');
        const avatarDiv = document.querySelector('.w-32.h-32:not(img)');
        
        if (url && isValidImageUrl(url)) {
            if (avatarImg) {
                avatarImg.src = url;
            } else if (avatarDiv) {
                avatarDiv.innerHTML = `<img src="${url}" alt="Avatar preview" class="w-32 h-32 rounded-full object-cover">`;
            }
        }
    });

    document.getElementById('{{ form.cover_url.id_for_label }}').addEventListener('input', function() {
        const url = this.value.trim();
        const coverImg = document.querySelector('.h-48 img');
        const coverDiv = document.querySelector('.h-48');
        
        if (url && isValidImageUrl(url)) {
            if (coverImg) {
                coverImg.src = url;
            } else {
                coverDiv.innerHTML = `<img src="${url}" alt="Cover preview" class="w-full h-full object-cover">
                                     <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                                         <button type="button" class="p-3 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors">
                                             <i class="fas fa-camera text-xl"></i>
                                         </button>
                                     </div>`;
            }
        }
    });

    function isValidImageUrl(url) {
        try {
            new URL(url);
            return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
        } catch {
            return false;
        }
    }

    // Character counter for bio
    const bioTextarea = document.getElementById('{{ form.bio.id_for_label }}');
    if (bioTextarea) {
        const maxLength = 160;
        const counter = document.createElement('div');
        counter.className = 'text-sm text-gray-500 dark:text-gray-400 text-right mt-1';
        counter.textContent = `${bioTextarea.value.length}/${maxLength}`;
        bioTextarea.parentNode.appendChild(counter);

        bioTextarea.addEventListener('input', function() {
            counter.textContent = `${this.value.length}/${maxLength}`;
            if (this.value.length > maxLength) {
                counter.classList.add('text-red-500');
                counter.classList.remove('text-gray-500', 'dark:text-gray-400');
            } else {
                counter.classList.remove('text-red-500');
                counter.classList.add('text-gray-500', 'dark:text-gray-400');
            }
        });
    }
</script>
{% endblock %}
