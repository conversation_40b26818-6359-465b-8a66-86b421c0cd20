from supabase import create_client, Client
from django.conf import settings
import os

def get_supabase_client() -> Client:
    """
    Create and return a Supabase client instance
    """
    url = settings.SUPABASE_URL
    key = settings.SUPABASE_KEY
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
    
    return create_client(url, key)

# Global client instance
supabase: Client = get_supabase_client()
