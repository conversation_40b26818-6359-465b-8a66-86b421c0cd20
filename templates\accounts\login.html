{% extends 'base.html' %}

{% block title %}Login - Twitter Clone{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-bg py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-twitter-blue">
                <i class="fab fa-twitter text-white text-2xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Or
                <a href="{% url 'accounts:register' %}" class="font-medium text-twitter-blue hover:text-twitter-dark-blue">
                    create a new account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="{{ form.username.id_for_label }}" class="sr-only">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.username.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                <div class="mt-4">
                    <label for="{{ form.password.id_for_label }}" class="sr-only">Password</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ form.password.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="text-sm text-red-600 dark:text-red-400">
                    {{ form.non_field_errors.0 }}
                </div>
            {% endif %}

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-twitter-blue focus:ring-twitter-blue border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                        Remember me
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" class="font-medium text-twitter-blue hover:text-twitter-dark-blue">
                        Forgot your password?
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-twitter-blue hover:bg-twitter-dark-blue focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-twitter-blue transition-colors">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-sign-in-alt text-twitter-dark-blue group-hover:text-twitter-blue"></i>
                    </span>
                    Sign in
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
