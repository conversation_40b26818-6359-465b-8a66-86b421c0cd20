{% extends 'base.html' %}

{% block title %}Tweet by {{ post.user.full_name }} - Twitter Clone{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto bg-white dark:bg-dark-secondary">
    <!-- Back Button -->
    <div class="flex items-center p-4 border-b border-gray-200 dark:border-dark-border">
        <button onclick="history.back()" class="mr-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-left text-gray-600 dark:text-gray-400"></i>
        </button>
        <h1 class="text-xl font-bold text-gray-900 dark:text-white">Tweet</h1>
    </div>

    <!-- Main Post -->
    <article class="p-4 border-b border-gray-200 dark:border-dark-border">
        <div class="flex space-x-3">
            <!-- User Avatar -->
            <div class="flex-shrink-0">
                {% if post.user.avatar_url %}
                    <img src="{{ post.user.avatar_url }}" alt="{{ post.user.username }}" class="w-12 h-12 rounded-full">
                {% else %}
                    <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                    </div>
                {% endif %}
            </div>
            
            <!-- Post Content -->
            <div class="flex-1 min-w-0">
                <!-- User Info -->
                <div class="flex items-center space-x-2 mb-3">
                    <a href="{% url 'accounts:profile' post.user.username %}" class="font-bold hover:underline text-gray-900 dark:text-white">
                        {{ post.user.full_name }}
                    </a>
                    <span class="text-gray-500 dark:text-gray-400">@{{ post.user.username }}</span>
                </div>
                
                <!-- Post Text -->
                <div class="mb-4">
                    <p class="text-xl text-gray-900 dark:text-white whitespace-pre-wrap leading-relaxed">{{ post.content }}</p>
                </div>
                
                <!-- Post Image -->
                {% if post.image_url %}
                <div class="mb-4">
                    <img src="{{ post.image_url }}" alt="Post image" class="rounded-2xl max-w-full h-auto border border-gray-200 dark:border-gray-600">
                </div>
                {% endif %}
                
                <!-- Post Timestamp -->
                <div class="mb-4 text-gray-500 dark:text-gray-400 text-sm">
                    {{ post.created_at|date:"g:i A · M d, Y" }}
                </div>
                
                <!-- Post Stats -->
                <div class="flex space-x-6 py-3 border-t border-b border-gray-200 dark:border-dark-border text-sm">
                    <div>
                        <span class="font-bold text-gray-900 dark:text-white">{{ post.get_comments_count }}</span>
                        <span class="text-gray-500 dark:text-gray-400 ml-1">Comments</span>
                    </div>
                    <div>
                        <span class="font-bold text-gray-900 dark:text-white">0</span>
                        <span class="text-gray-500 dark:text-gray-400 ml-1">Retweets</span>
                    </div>
                    <div>
                        <span class="font-bold text-gray-900 dark:text-white" id="likes-count">{{ post.get_likes_count }}</span>
                        <span class="text-gray-500 dark:text-gray-400 ml-1">Likes</span>
                    </div>
                </div>
                
                <!-- Post Actions -->
                <div class="flex items-center justify-around py-3 border-b border-gray-200 dark:border-dark-border">
                    <!-- Comment -->
                    <button onclick="focusCommentInput()" class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                        <div class="p-3 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                            <i class="far fa-comment text-xl"></i>
                        </div>
                    </button>
                    
                    <!-- Retweet -->
                    <button class="flex items-center space-x-2 hover:text-green-500 transition-colors group">
                        <div class="p-3 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900">
                            <i class="fas fa-retweet text-xl"></i>
                        </div>
                    </button>
                    
                    <!-- Like -->
                    {% if user.is_authenticated %}
                    <button onclick="likePost('{{ post.id }}')" class="flex items-center space-x-2 hover:text-red-500 transition-colors group {% if post.is_liked_by:user %}text-red-500{% endif %}">
                        <div class="p-3 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900">
                            <i class="{% if post.is_liked_by:user %}fas{% else %}far{% endif %} fa-heart text-xl" id="heart-icon"></i>
                        </div>
                    </button>
                    {% else %}
                    <div class="flex items-center space-x-2 text-gray-400">
                        <div class="p-3 rounded-full">
                            <i class="far fa-heart text-xl"></i>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Share -->
                    <button class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                        <div class="p-3 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                            <i class="fas fa-share text-xl"></i>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </article>

    <!-- Add Comment Section -->
    {% if user.is_authenticated %}
    <div class="p-4 border-b border-gray-200 dark:border-dark-border">
        <div class="flex space-x-3">
            {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-12 h-12 rounded-full">
            {% else %}
                <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                </div>
            {% endif %}
            
            <div class="flex-1">
                <form id="commentForm" class="space-y-3">
                    {% csrf_token %}
                    <textarea id="commentInput" name="content" placeholder="Tweet your reply" maxlength="280" 
                              class="w-full p-3 text-xl placeholder-gray-500 dark:placeholder-gray-400 bg-transparent border-none resize-none focus:outline-none dark:text-white" 
                              rows="3"></textarea>
                    
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            <span id="commentCharCount">0</span>/280
                        </div>
                        
                        <button type="submit" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors disabled:opacity-50">
                            Reply
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Comments -->
    <div id="commentsContainer">
        {% for comment in comments %}
        <article class="p-4 border-b border-gray-200 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div class="flex space-x-3">
                <!-- User Avatar -->
                <div class="flex-shrink-0">
                    {% if comment.user.avatar_url %}
                        <img src="{{ comment.user.avatar_url }}" alt="{{ comment.user.username }}" class="w-10 h-10 rounded-full">
                    {% else %}
                        <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-sm text-gray-600 dark:text-gray-400"></i>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Comment Content -->
                <div class="flex-1 min-w-0">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2 mb-1">
                        <a href="{% url 'accounts:profile' comment.user.username %}" class="font-bold hover:underline text-gray-900 dark:text-white">
                            {{ comment.user.full_name }}
                        </a>
                        <span class="text-gray-500 dark:text-gray-400">@{{ comment.user.username }}</span>
                        <span class="text-gray-500 dark:text-gray-400">·</span>
                        <span class="text-gray-500 dark:text-gray-400">{{ comment.get_time_since_posted }}</span>
                    </div>
                    
                    <!-- Comment Text -->
                    <div>
                        <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ comment.content }}</p>
                    </div>
                </div>
            </div>
        </article>
        {% empty %}
        <div class="p-8 text-center text-gray-500 dark:text-gray-400">
            <i class="far fa-comment text-4xl mb-4"></i>
            <h3 class="text-xl font-semibold mb-2">No replies yet</h3>
            <p>Be the first to reply to this tweet!</p>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Like post functionality
    function likePost(postId) {
        fetch(`/posts/${postId}/like/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            const likesCount = document.getElementById('likes-count');
            const heartIcon = document.getElementById('heart-icon');
            const likeButton = heartIcon.closest('button');
            
            likesCount.textContent = data.likes_count;
            
            if (data.is_liked) {
                likeButton.classList.add('text-red-500');
                heartIcon.classList.remove('far');
                heartIcon.classList.add('fas');
            } else {
                likeButton.classList.remove('text-red-500');
                heartIcon.classList.remove('fas');
                heartIcon.classList.add('far');
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // Comment functionality
    document.getElementById('commentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const content = document.getElementById('commentInput').value.trim();
        if (!content) return;
        
        fetch(`/posts/{{ post.id }}/comment/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `content=${encodeURIComponent(content)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add new comment to the list
                const commentsContainer = document.getElementById('commentsContainer');
                const newComment = createCommentElement(data.comment);
                
                // Remove "no replies" message if it exists
                const noReplies = commentsContainer.querySelector('.text-center');
                if (noReplies) {
                    noReplies.remove();
                }
                
                commentsContainer.insertAdjacentHTML('afterbegin', newComment);
                
                // Clear the form
                document.getElementById('commentInput').value = '';
                document.getElementById('commentCharCount').textContent = '0';
                
                // Update comments count in stats
                const statsComments = document.querySelector('.border-t.border-b .font-bold');
                if (statsComments) {
                    statsComments.textContent = data.comments_count;
                }
            }
        })
        .catch(error => console.error('Error:', error));
    });

    // Character counter for comments
    document.getElementById('commentInput').addEventListener('input', function() {
        const charCount = document.getElementById('commentCharCount');
        charCount.textContent = this.value.length;
        
        if (this.value.length > 280) {
            charCount.classList.add('text-red-500');
        } else {
            charCount.classList.remove('text-red-500');
        }
    });

    // Focus comment input
    function focusCommentInput() {
        document.getElementById('commentInput').focus();
    }

    // Create comment element HTML
    function createCommentElement(comment) {
        const avatarHtml = comment.user.avatar_url 
            ? `<img src="${comment.user.avatar_url}" alt="${comment.user.username}" class="w-10 h-10 rounded-full">`
            : `<div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                 <i class="fas fa-user text-sm text-gray-600 dark:text-gray-400"></i>
               </div>`;
        
        return `
            <article class="p-4 border-b border-gray-200 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        ${avatarHtml}
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/accounts/profile/${comment.user.username}/" class="font-bold hover:underline text-gray-900 dark:text-white">
                                ${comment.user.display_name}
                            </a>
                            <span class="text-gray-500 dark:text-gray-400">@${comment.user.username}</span>
                            <span class="text-gray-500 dark:text-gray-400">·</span>
                            <span class="text-gray-500 dark:text-gray-400">${comment.created_at}</span>
                        </div>
                        <div>
                            <p class="text-gray-900 dark:text-white whitespace-pre-wrap">${comment.content}</p>
                        </div>
                    </div>
                </div>
            </article>
        `;
    }
</script>
{% endblock %}
