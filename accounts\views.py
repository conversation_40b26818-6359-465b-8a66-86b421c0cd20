from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from .forms import CustomUserCreationForm, CustomAuthenticationForm, ProfileUpdateForm
from posts.models import Post
from follows.models import Follow

User = get_user_model()

def register_view(request):
    """User registration view"""
    if request.user.is_authenticated:
        return redirect('posts:home')

    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}!')
            login(request, user)
            return redirect('posts:home')
    else:
        form = CustomUserCreationForm()

    return render(request, 'accounts/register.html', {'form': form})

def login_view(request):
    """User login view"""
    if request.user.is_authenticated:
        return redirect('posts:home')

    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.info(request, f"You are now logged in as {username}.")
                return redirect('posts:home')
            else:
                messages.error(request, "Invalid username or password.")
        else:
            messages.error(request, "Invalid username or password.")
    else:
        form = CustomAuthenticationForm()

    return render(request, 'accounts/login.html', {'form': form})

def logout_view(request):
    """User logout view"""
    logout(request)
    messages.info(request, "You have successfully logged out.")
    return redirect('posts:home')

def profile_view(request, username):
    """User profile view"""
    user = get_object_or_404(User, username=username)
    posts = Post.objects.filter(user=user).select_related('user').prefetch_related('likes', 'comments')

    # Check if current user follows this profile user
    is_following = False
    if request.user.is_authenticated and request.user != user:
        is_following = Follow.objects.filter(follower=request.user, following=user).exists()

    # Add is_liked information for each post
    if request.user.is_authenticated:
        for post in posts:
            post.is_liked = post.likes.filter(user=request.user).exists()
    else:
        for post in posts:
            post.is_liked = False

    context = {
        'profile_user': user,
        'posts': posts,
        'is_following': is_following,
        'is_own_profile': request.user == user,
    }

    return render(request, 'accounts/profile.html', context)

@login_required
def edit_profile_view(request):
    """Edit user profile view"""
    if request.method == 'POST':
        form = ProfileUpdateForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your profile has been updated!')
            return redirect('profile', username=request.user.username)
    else:
        form = ProfileUpdateForm(instance=request.user)

    return render(request, 'accounts/edit_profile.html', {'form': form})

@login_required
@require_http_methods(["POST"])
def follow_user(request, username):
    """Follow/unfollow a user via AJAX"""
    user_to_follow = get_object_or_404(User, username=username)

    if request.user == user_to_follow:
        return JsonResponse({'error': 'You cannot follow yourself'}, status=400)

    follow_obj, created = Follow.objects.get_or_create(
        follower=request.user,
        following=user_to_follow
    )

    if not created:
        # Already following, so unfollow
        follow_obj.delete()
        is_following = False
        action = 'unfollowed'
    else:
        is_following = True
        action = 'followed'

    return JsonResponse({
        'is_following': is_following,
        'action': action,
        'followers_count': user_to_follow.get_followers_count(),
        'following_count': user_to_follow.get_following_count()
    })
