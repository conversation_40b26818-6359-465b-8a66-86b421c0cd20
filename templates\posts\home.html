{% extends 'base.html' %}

{% block title %}Home - Twitter Clone{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Create Post Section (for authenticated users) -->
    {% if user.is_authenticated %}
    <div class="bg-white dark:bg-dark-secondary border-b border-gray-200 dark:border-dark-border p-4">
        <div class="flex space-x-3">
            {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-12 h-12 rounded-full">
            {% else %}
                <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                </div>
            {% endif %}
            
            <div class="flex-1">
                <form method="post" action="{% url 'posts:create_post' %}" class="space-y-3">
                    {% csrf_token %}
                    <textarea name="content" placeholder="What's happening?" maxlength="280" 
                              class="w-full p-3 text-xl placeholder-gray-500 dark:placeholder-gray-400 bg-transparent border-none resize-none focus:outline-none dark:text-white" 
                              rows="3" required></textarea>
                    
                    <input type="url" name="image_url" placeholder="Add image URL (optional)" 
                           class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-twitter-blue dark:bg-dark-bg dark:text-white">
                    
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4 text-twitter-blue">
                            <i class="fas fa-image cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 p-2 rounded-full"></i>
                            <i class="fas fa-poll cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 p-2 rounded-full"></i>
                            <i class="fas fa-smile cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 p-2 rounded-full"></i>
                        </div>
                        
                        <button type="submit" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors disabled:opacity-50">
                            Tweet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Posts Feed -->
    <div class="bg-white dark:bg-dark-secondary">
        {% for post in posts %}
        <article class="border-b border-gray-200 dark:border-dark-border p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div class="flex space-x-3">
                <!-- User Avatar -->
                <div class="flex-shrink-0">
                    {% if post.user.avatar_url %}
                        <img src="{{ post.user.avatar_url }}" alt="{{ post.user.username }}" class="w-12 h-12 rounded-full">
                    {% else %}
                        <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Post Content -->
                <div class="flex-1 min-w-0">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2 mb-1">
                        <a href="{% url 'accounts:profile' post.user.username %}" class="font-bold hover:underline">
                            {{ post.user.full_name }}
                        </a>
                        <span class="text-gray-500 dark:text-gray-400">@{{ post.user.username }}</span>
                        <span class="text-gray-500 dark:text-gray-400">·</span>
                        <span class="text-gray-500 dark:text-gray-400">{{ post.get_time_since_posted }}</span>
                        
                        {% if user == post.user %}
                        <div class="ml-auto">
                            <button onclick="togglePostMenu('{{ post.id }}')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <div id="postMenu-{{ post.id }}" class="hidden absolute right-0 mt-2 w-32 bg-white dark:bg-dark-secondary rounded-md shadow-lg py-1 z-10">
                                <form method="post" action="{% url 'posts:delete_post' post.id %}" class="inline">
                                    {% csrf_token %}
                                    <button type="submit" onclick="return confirm('Are you sure you want to delete this tweet?')" 
                                            class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600">
                                        <i class="fas fa-trash mr-2"></i>Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Post Text -->
                    <div class="mb-3">
                        <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ post.content }}</p>
                    </div>
                    
                    <!-- Post Image -->
                    {% if post.image_url %}
                    <div class="mb-3">
                        <img src="{{ post.image_url }}" alt="Post image" class="rounded-2xl max-w-full h-auto border border-gray-200 dark:border-gray-600">
                    </div>
                    {% endif %}
                    
                    <!-- Post Actions -->
                    <div class="flex items-center justify-between max-w-md text-gray-500 dark:text-gray-400">
                        <!-- Comments -->
                        <a href="{% url 'posts:post_detail' post.id %}" class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                                <i class="far fa-comment"></i>
                            </div>
                            <span class="text-sm">{{ post.get_comments_count }}</span>
                        </a>
                        
                        <!-- Retweet -->
                        <button class="flex items-center space-x-2 hover:text-green-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900">
                                <i class="fas fa-retweet"></i>
                            </div>
                            <span class="text-sm">0</span>
                        </button>
                        
                        <!-- Like -->
                        {% if user.is_authenticated %}
                        <button onclick="likePost('{{ post.id }}')" class="flex items-center space-x-2 hover:text-red-500 transition-colors group {% if post.is_liked %}text-red-500{% endif %}">
                            <div class="p-2 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900">
                                <i class="{% if post.is_liked %}fas{% else %}far{% endif %} fa-heart"></i>
                            </div>
                            <span class="text-sm" id="likes-count-{{ post.id }}">{{ post.get_likes_count }}</span>
                        </button>
                        {% else %}
                        <div class="flex items-center space-x-2 text-gray-400">
                            <div class="p-2 rounded-full">
                                <i class="far fa-heart"></i>
                            </div>
                            <span class="text-sm">{{ post.get_likes_count }}</span>
                        </div>
                        {% endif %}
                        
                        <!-- Share -->
                        <button class="flex items-center space-x-2 hover:text-blue-500 transition-colors group">
                            <div class="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900">
                                <i class="fas fa-share"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </article>
        {% empty %}
        <div class="p-8 text-center text-gray-500 dark:text-gray-400">
            <i class="fas fa-twitter text-4xl mb-4"></i>
            <h3 class="text-xl font-semibold mb-2">Welcome to Twitter Clone!</h3>
            {% if user.is_authenticated %}
                <p>Start following people to see their tweets in your timeline.</p>
            {% else %}
                <p>Sign up to start tweeting and following people.</p>
                <div class="mt-4 space-x-4">
                    <a href="{% url 'accounts:register' %}" class="bg-twitter-blue hover:bg-twitter-dark-blue text-white px-6 py-2 rounded-full font-semibold transition-colors">
                        Sign up
                    </a>
                    <a href="{% url 'accounts:login' %}" class="border border-twitter-blue text-twitter-blue hover:bg-twitter-blue hover:text-white px-6 py-2 rounded-full font-semibold transition-colors">
                        Log in
                    </a>
                </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="bg-white dark:bg-dark-secondary border-t border-gray-200 dark:border-dark-border p-4">
        <div class="flex justify-center space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600">
                    Previous
                </a>
            {% endif %}
            
            <span class="px-3 py-2 text-gray-500 dark:text-gray-400">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600">
                    Next
                </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Like post functionality
    function likePost(postId) {
        fetch(`/posts/${postId}/like/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            const likesCount = document.getElementById(`likes-count-${postId}`);
            const likeButton = likesCount.parentElement;
            const heartIcon = likeButton.querySelector('i');
            
            likesCount.textContent = data.likes_count;
            
            if (data.is_liked) {
                likeButton.classList.add('text-red-500');
                heartIcon.classList.remove('far');
                heartIcon.classList.add('fas');
            } else {
                likeButton.classList.remove('text-red-500');
                heartIcon.classList.remove('fas');
                heartIcon.classList.add('far');
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // Toggle post menu
    function togglePostMenu(postId) {
        const menu = document.getElementById(`postMenu-${postId}`);
        menu.classList.toggle('hidden');
    }

    // Close post menus when clicking outside
    document.addEventListener('click', function(event) {
        const menus = document.querySelectorAll('[id^="postMenu-"]');
        menus.forEach(menu => {
            const button = event.target.closest(`button[onclick*="${menu.id.split('-')[1]}"]`);
            if (!button && !menu.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });
    });
</script>
{% endblock %}
