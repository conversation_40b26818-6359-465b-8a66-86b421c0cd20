from django.db import models
from django.contrib.auth.models import AbstractUser
import uuid

class CustomUser(AbstractUser):
    """
    Custom User model that extends Django's AbstractUser
    This will be synced with Supabase profiles table
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = models.CharField(max_length=50, unique=True)
    display_name = models.CharField(max_length=100, blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    avatar_url = models.URLField(blank=True, null=True)
    cover_url = models.URLField(blank=True, null=True)
    location = models.CharField(max_length=100, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"@{self.username}"

    @property
    def full_name(self):
        return self.display_name or self.username

    def get_followers_count(self):
        """Get number of followers"""
        from follows.models import Follow
        return Follow.objects.filter(following=self).count()

    def get_following_count(self):
        """Get number of users this user is following"""
        from follows.models import Follow
        return Follow.objects.filter(follower=self).count()

    def get_posts_count(self):
        """Get number of posts by this user"""
        from posts.models import Post
        return Post.objects.filter(user=self).count()

    class Meta:
        db_table = 'profiles'
